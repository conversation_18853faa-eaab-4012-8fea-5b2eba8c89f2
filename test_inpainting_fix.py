#!/usr/bin/env python3
"""
测试图像补全功能修复
"""

import numpy as np
from PIL import Image
import os

def test_create_inpainting_mask():
    """测试create_inpainting_mask函数"""
    print("🧪 测试图像补全mask创建...")
    
    try:
        from utils import create_inpainting_mask
        
        # 创建测试数据
        image_shape_3d = (256, 256, 3)  # 3D shape
        image_shape_2d = (256, 256)      # 2D shape
        
        # 创建模拟的selected_masks
        selected_masks = [
            {
                'segmentation': np.zeros((256, 256), dtype=bool)
            }
        ]
        selected_masks[0]['segmentation'][100:150, 100:150] = True  # 创建一个方形区域
        
        # 测试3D shape
        print("测试3D图像shape...")
        mask_3d = create_inpainting_mask(selected_masks, image_shape_3d)
        print(f"✅ 3D shape测试通过，mask形状: {mask_3d.shape}")
        
        # 测试2D shape
        print("测试2D图像shape...")
        mask_2d = create_inpainting_mask(selected_masks, image_shape_2d)
        print(f"✅ 2D shape测试通过，mask形状: {mask_2d.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inpainting_model():
    """测试补全模型"""
    print("\n🧪 测试补全模型...")
    
    try:
        from inpainting_model import LAMAInpainter
        
        # 创建测试图像和mask
        test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        test_mask = np.zeros((256, 256), dtype=np.uint8)
        test_mask[100:150, 100:150] = 255  # 创建一个方形补全区域
        
        # 测试补全
        inpainter = LAMAInpainter()
        result = inpainter.inpaint(test_image, test_mask, method="telea")
        
        print(f"✅ 补全模型测试通过，结果形状: {result.size}")
        return True
        
    except Exception as e:
        print(f"❌ 补全模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 图像补全功能修复测试")
    print("=" * 50)
    
    # 测试mask创建
    mask_test = test_create_inpainting_mask()
    
    # 测试补全模型
    model_test = test_inpainting_model()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   - Mask创建测试: {'✅ 通过' if mask_test else '❌ 失败'}")
    print(f"   - 补全模型测试: {'✅ 通过' if model_test else '❌ 失败'}")
    
    if mask_test and model_test:
        print("\n🎉 所有测试通过！补全功能已修复！")
        print("💡 现在可以在网页中正常使用补全功能了")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main() 