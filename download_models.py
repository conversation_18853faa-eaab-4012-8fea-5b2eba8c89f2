#!/usr/bin/env python3
"""
SAM2 模型下载脚本
自动下载所有SAM2模型：tiny、small、base_plus、large
"""

import os
import torch
from sam_model import SAMPredictor

def download_all_models():
    """下载所有SAM2模型"""
    models = ["tiny", "small", "base_plus", "large"]
    
    # 模型大小信息
    model_sizes = {
        "tiny": "~40MB",
        "small": "~110MB", 
        "base_plus": "~220MB",
        "large": "~900MB"
    }
    
    print("🚀 开始下载SAM2模型...")
    print("=" * 60)
    
    for model_name in models:
        try:
            print(f"\n📦 正在下载 {model_name} 模型 ({model_sizes[model_name]})...")
            
            # 初始化预测器会自动下载模型
            predictor = SAMPredictor(model_size=model_name)
            
            # 检查模型是否加载成功
            if predictor.predictor is not None:
                print(f"✅ {model_name} 模型下载完成!")
            else:
                print(f"❌ {model_name} 模型下载失败")
                
        except Exception as e:
            print(f"❌ {model_name} 模型下载失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 所有模型下载完成!")
    
    # 显示下载的模型文件
    checkpoint_dir = "./checkpoints"
    if os.path.exists(checkpoint_dir):
        print(f"\n📁 模型文件保存在: {checkpoint_dir}")
        for file in os.listdir(checkpoint_dir):
            if file.endswith('.pt'):
                file_path = os.path.join(checkpoint_dir, file)
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"   {file} ({file_size:.1f} MB)")
    
    print("\n🚀 现在可以启动应用并使用所有模型了！")
    print("   运行: python app.py")

if __name__ == "__main__":
    download_all_models() 