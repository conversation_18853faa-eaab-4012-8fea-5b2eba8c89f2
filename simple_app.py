import gradio as gr
import numpy as np
from PIL import Image
import tempfile
import os

def simple_cutout_demo(image):
    """简化版抠图演示"""
    if image is None:
        return None, "请先上传图片"
    
    # 创建一个简单的演示效果 - 转换为灰度图
    gray_image = image.convert('L')
    
    # 创建一个简单的透明效果
    rgba_image = Image.new('RGBA', image.size, (255, 255, 255, 0))
    
    # 在中心创建一个圆形区域
    width, height = image.size
    center_x, center_y = width // 2, height // 2
    radius = min(width, height) // 4
    
    # 将原图的中心圆形区域复制到透明图像上
    for y in range(height):
        for x in range(width):
            distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
            if distance <= radius:
                rgba_image.putpixel((x, y), image.getpixel((x, y)) + (255,))
    
    return rgba_image, "演示抠图完成！(这是一个简化版本，实际版本需要等待SAM2模型安装完成)"

def create_simple_interface():
    """创建简化版界面"""
    with gr.Blocks(title="AI智能抠图工具 - 简化版") as demo:
        gr.Markdown("""
        # 🎨 AI智能抠图工具 - 简化版
        
        ## 📢 状态说明
        - ⏳ SAM2模型正在后台安装中...
        - 🔄 当前显示的是简化版演示
        - ✅ 完整版本将在模型安装完成后可用
        
        ## 🔧 当前功能
        - 图片上传和预览
        - 简单的圆形抠图演示
        - 透明背景PNG导出
        
        ---
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📤 上传图片")
                input_image = gr.Image(
                    type="pil",
                    label="选择图片",
                    sources=["upload"],
                    interactive=True
                )
                
                process_btn = gr.Button("🔄 处理图片", variant="primary", size="lg")
                
                gr.Markdown("### ℹ️ 提示")
                gr.Markdown("""
                - 当前为演示版本
                - 将在图片中心创建圆形抠图
                - 实际版本支持智能区域选择
                """)
                
            with gr.Column(scale=2):
                gr.Markdown("### 🖼️ 抠图结果")
                output_image = gr.Image(
                    type="pil",
                    label="抠图结果",
                    interactive=False
                )
                
                download_btn = gr.DownloadButton(
                    "💾 下载透明背景图片",
                    variant="secondary",
                    size="lg"
                )
        
        # 状态显示
        status_display = gr.Textbox(
            label="状态信息",
            value="简化版已就绪，等待图片上传...",
            interactive=False,
            max_lines=3
        )
        
        # 事件处理
        process_btn.click(
            fn=simple_cutout_demo,
            inputs=[input_image],
            outputs=[output_image, status_display]
        )
        
        # 自动处理
        input_image.change(
            fn=simple_cutout_demo,
            inputs=[input_image],
            outputs=[output_image, status_display]
        )
    
    return demo

def main():
    """主函数"""
    print("🚀 启动AI智能抠图工具 - 简化版...")
    print("=" * 50)
    print("📦 SAM2模型正在后台安装中...")
    print("🔄 当前运行简化版演示")
    print("✅ 完整版本将在模型安装完成后可用")
    print("=" * 50)
    
    # 创建和启动界面
    demo = create_simple_interface()
    
    # 启动应用
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_api=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main() 