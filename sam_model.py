import torch
import numpy as np
import cv2
from PIL import Image
import os
from typing import Tuple, List, Optional

try:
    from sam2.build_sam import build_sam2
    from sam2.sam2_image_predictor import SAM2ImagePredictor
except ImportError:
    print("SAM2 not installed. Please install it using: pip install git+https://github.com/facebookresearch/sam2.git")
    raise

class SAMPredictor:
    def __init__(self, model_size: str = "large", device: str = "auto"):
        """
        Initialize SAM2 predictor
        
        Args:
            model_size: Model size ('tiny', 'small', 'base_plus', 'large')
            device: Device to run on ('auto', 'cpu', 'cuda')
        """
        self.device = self._get_device(device)
        self.model_size = model_size
        self.predictor = None
        self.image = None
        self.image_embeddings = None
        
        # Load model
        self._load_model()
    
    def _get_device(self, device: str) -> str:
        """Get appropriate device"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def _load_model(self):
        """Load SAM2 model"""
        try:
            # Model configurations
            model_configs = {
                "tiny": "sam2_hiera_t.yaml",
                "small": "sam2_hiera_s.yaml", 
                "base_plus": "sam2_hiera_b+.yaml",
                "large": "sam2_hiera_l.yaml"
            }
            
            checkpoint_urls = {
                "tiny": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_tiny.pt",
                "small": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_small.pt",
                "base_plus": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_base_plus.pt",
                "large": "https://dl.fbaipublicfiles.com/segment_anything_2/072824/sam2_hiera_large.pt"
            }
            
            config_name = model_configs[self.model_size]
            checkpoint_url = checkpoint_urls[self.model_size]
            
            # Download checkpoint if not exists
            checkpoint_path = f"./checkpoints/sam2_{self.model_size}.pt"
            if not os.path.exists(checkpoint_path):
                print(f"Downloading SAM2 {self.model_size} checkpoint...")
                os.makedirs("./checkpoints", exist_ok=True)
                torch.hub.download_url_to_file(checkpoint_url, checkpoint_path)
            
            # Build model
            sam2_model = build_sam2(config_name, checkpoint_path, device=self.device)
            self.predictor = SAM2ImagePredictor(sam2_model)
            
            print(f"SAM2 {self.model_size} model loaded successfully on {self.device}")
            
        except Exception as e:
            print(f"Error loading SAM2 model: {e}")
            # Fallback to a simpler initialization
            self.predictor = None
            raise
    
    def set_image(self, image: np.ndarray):
        """Set image for prediction"""
        if self.predictor is None:
            raise RuntimeError("Model not loaded properly")
        
        self.image = image
        self.predictor.set_image(image)
        print(f"Image set successfully. Shape: {image.shape}")
    
    def predict_point(self, point: Tuple[int, int], point_label: int = 1) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Predict mask for a single point
        
        Args:
            point: (x, y) coordinates
            point_label: 1 for foreground, 0 for background
            
        Returns:
            masks: Array of predicted masks
            scores: Confidence scores
            logits: Raw logits
        """
        if self.predictor is None:
            raise RuntimeError("Model not loaded properly")
        
        input_point = np.array([[point[0], point[1]]])
        input_label = np.array([point_label])
        
        masks, scores, logits = self.predictor.predict(
            point_coords=input_point,
            point_labels=input_label,
            multimask_output=True,
        )
        
        return masks, scores, logits
    
    def predict_everything(self) -> List[dict]:
        """
        Generate masks for everything in the image
        
        Returns:
            List of mask dictionaries
        """
        if self.predictor is None:
            raise RuntimeError("Model not loaded properly")
        
        try:
            # Use automatic mask generation
            from sam2.automatic_mask_generator import SAM2AutomaticMaskGenerator
            mask_generator = SAM2AutomaticMaskGenerator(self.predictor.model)
            masks = mask_generator.generate(self.image)
            return masks
        except Exception as e:
            print(f"Error in automatic mask generation: {e}")
            # Fallback: generate masks using grid sampling
            return self._generate_grid_masks()
    
    def _generate_grid_masks(self) -> List[dict]:
        """Fallback method to generate masks using grid sampling"""
        if self.image is None:
            return []
        
        h, w = self.image.shape[:2]
        masks = []
        
        # Sample points in a grid
        grid_size = 32
        for y in range(grid_size, h - grid_size, grid_size):
            for x in range(grid_size, w - grid_size, grid_size):
                try:
                    mask_array, scores, _ = self.predict_point((x, y))
                    if len(mask_array) > 0:
                        # Take the best mask
                        best_idx = np.argmax(scores)
                        mask = mask_array[best_idx]
                        if mask.sum() > 100:  # Filter out tiny masks
                            masks.append({
                                'segmentation': mask,
                                'area': mask.sum(),
                                'bbox': self._mask_to_bbox(mask),
                                'predicted_iou': scores[best_idx],
                                'point_coords': [x, y],
                                'stability_score': scores[best_idx],
                                'crop_box': [0, 0, w, h]
                            })
                except Exception as e:
                    continue
        
        return masks
    
    def _mask_to_bbox(self, mask: np.ndarray) -> List[int]:
        """Convert mask to bounding box [x, y, width, height]"""
        rows = np.any(mask, axis=1)
        cols = np.any(mask, axis=0)
        
        if not rows.any() or not cols.any():
            return [0, 0, 0, 0]
        
        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]
        
        return [int(cmin), int(rmin), int(cmax - cmin), int(rmax - rmin)] 