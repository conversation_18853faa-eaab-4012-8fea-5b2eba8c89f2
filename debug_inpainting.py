#!/usr/bin/env python3
"""
调试图像补全功能
"""

import os
import numpy as np
from PIL import Image
import cv2
import time
import requests
import subprocess
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_inpainting_dependencies():
    """测试补全功能依赖"""
    print("🔍 检查补全功能依赖...")
    
    try:
        # 检查关键模块
        import cv2
        import numpy as np
        from PIL import Image
        
        # 检查utils模块
        from utils import create_inpainting_mask
        
        # 检查inpainting模块
        from inpainting_model import LAMAInpainter
        
        print("✅ 所有依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def test_inpainting_function():
    """测试补全功能核心逻辑"""
    print("\n🧪 测试补全功能核心逻辑...")
    
    try:
        # 导入所需模块
        from utils import create_inpainting_mask
        from inpainting_model import LAMAInpainter
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        
        # 创建测试mask
        selected_masks = [
            {
                'segmentation': np.zeros((256, 256), dtype=bool)
            }
        ]
        selected_masks[0]['segmentation'][100:150, 100:150] = True
        
        # 测试create_inpainting_mask函数
        print("测试create_inpainting_mask函数...")
        mask = create_inpainting_mask(selected_masks, test_image.shape)
        print(f"✅ mask创建成功，形状: {mask.shape}")
        
        # 测试LAMAInpainter
        print("测试LAMAInpainter...")
        inpainter = LAMAInpainter()
        
        # 准备mask
        mask_uint8 = (mask * 255).astype(np.uint8)
        
        # 执行补全
        result = inpainter.inpaint(test_image, mask_uint8, method="telea")
        print(f"✅ 补全成功，结果尺寸: {result.size}")
        
        # 保存结果以供查看
        os.makedirs("test_outputs", exist_ok=True)
        Image.fromarray(test_image).save("test_outputs/debug_input.png")
        Image.fromarray(mask_uint8).save("test_outputs/debug_mask.png")
        result.save("test_outputs/debug_result.png")
        
        print("✅ 测试结果已保存到test_outputs目录")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_inpainting():
    """测试应用中的补全功能"""
    print("\n🧪 测试应用中的补全功能...")
    
    try:
        # 导入应用类
        from app_inpainting_complete import AIImageInpaintingComplete
        
        # 创建应用实例
        app = AIImageInpaintingComplete(model_size="tiny")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        test_pil = Image.fromarray(test_image)
        
        # 设置图像
        app.current_image = test_image
        app.display_image = test_image
        
        # 创建测试mask
        app.selected_masks = [
            {
                'segmentation': np.zeros((256, 256), dtype=bool)
            }
        ]
        app.selected_masks[0]['segmentation'][100:150, 100:150] = True
        
        # 执行补全
        print("执行execute_inpainting函数...")
        result_image, status_msg, output_path = app.execute_inpainting()
        
        if result_image is not None:
            print(f"✅ 应用补全成功: {status_msg}")
            print(f"✅ 输出路径: {output_path}")
            return True
        else:
            print(f"❌ 应用补全失败: {status_msg}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def restart_app():
    """重启应用"""
    print("\n🔄 尝试重启应用...")
    
    try:
        # 检查是否有Python进程运行
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True)
        
        if 'python.exe' in result.stdout:
            print("⚠️  检测到运行中的Python进程")
            response = input("是否要结束这些进程？(y/N): ")
            if response.lower() == 'y':
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'])
                print("✅ 已结束Python进程")
                time.sleep(2)  # 等待进程完全结束
        
        # 启动应用
        print("🚀 启动应用...")
        subprocess.Popen([sys.executable, "app_inpainting_complete.py"], 
                        creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        print("✅ 应用启动命令已发送")
        print("💡 请等待几秒钟，然后在浏览器中访问: http://localhost:7870")
        
        return True
    except Exception as e:
        print(f"❌ 重启失败: {e}")
        return False

def clear_browser_cache():
    """提示清除浏览器缓存"""
    print("\n🧹 浏览器缓存问题")
    print("如果应用仍然无法正常工作，请尝试:")
    print("1. 清除浏览器缓存")
    print("2. 使用无痕/隐私模式打开")
    print("3. 尝试不同的浏览器")
    print("4. 按Ctrl+F5强制刷新页面")

def main():
    """主函数"""
    print("🔧 图像补全功能调试工具")
    print("=" * 50)
    
    # 测试依赖
    deps_ok = test_inpainting_dependencies()
    
    # 测试核心逻辑
    if deps_ok:
        core_ok = test_inpainting_function()
    else:
        core_ok = False
        print("⚠️  依赖检查失败，跳过核心逻辑测试")
    
    # 测试应用功能
    if deps_ok and core_ok:
        app_ok = test_app_inpainting()
    else:
        app_ok = False
        print("⚠️  前序测试失败，跳过应用功能测试")
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   - 依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"   - 核心逻辑: {'✅ 通过' if core_ok else '❌ 失败'}")
    print(f"   - 应用功能: {'✅ 通过' if app_ok else '❌ 失败'}")
    
    # 提供解决方案
    if deps_ok and core_ok and app_ok:
        print("\n🎉 所有测试通过！补全功能应该可以正常工作")
        print("💡 如果网页中仍然无法使用，可能是浏览器缓存问题")
        clear_browser_cache()
        
        # 询问是否重启应用
        response = input("\n是否要重启应用？(y/N): ")
        if response.lower() == 'y':
            restart_app()
    else:
        print("\n⚠️  测试未全部通过，需要修复问题")
        
        # 询问是否重启应用
        response = input("\n是否要尝试重启应用？(y/N): ")
        if response.lower() == 'y':
            restart_app()

if __name__ == "__main__":
    main() 