# 🎨 AI智能抠图工具

基于SAM2（Segment Anything Model 2）的智能图像分割和抠图工具，支持一键抠图和透明背景PNG导出。

## ✨ 主要特性

- 🤖 **智能分割**：基于SAM2模型的自动图像分割
- 🖱️ **一键抠图**：点击即可完成抠图操作
- 🔄 **多选模式**：支持选择多个区域并合并导出
- 🎨 **边缘平滑**：新增强化边缘平滑处理，提升抠图质量
- 📥 **透明背景**：直接导出RGBA格式的透明背景PNG
- 🌐 **Web界面**：基于Gradio的直观用户界面
- 💻 **本地运行**：完全本地部署，保护隐私

## 🎯 边缘平滑功能

### 三种边缘处理方法
1. **基础形态学（Basic）**：使用OpenCV形态学操作平滑边缘
2. **软边缘（Soft）**：基于距离变换的边缘羽化效果
3. **综合处理（Comprehensive）**：结合多种技术的最佳效果

### 可调参数
- **平滑强度（1-10）**：控制边缘平滑的程度
- **羽化半径（1-10）**：控制边缘羽化的范围
- **抗锯齿处理**：通过超采样消除锯齿边缘

### 技术特点
- 🔄 **多层处理**：形态学操作 → 抗锯齿 → 边缘羽化
- 📐 **自适应缩放**：自动处理不同尺寸图像
- 🎛️ **实时调节**：UI界面实时调整参数
- 🎨 **质量优化**：显著改善抠图边缘质量

## 🏗️ 系统架构

```
segment/
├── app.py                  # 主应用程序（Gradio界面）
├── sam_model.py           # SAM2模型封装
├── utils.py               # 图像处理工具函数（含边缘平滑）
├── requirements.txt       # 依赖包列表
├── download_models.py     # 模型下载脚本
├── test_edge_smoothing.py # 边缘平滑功能测试
├── checkpoints/           # SAM2模型文件
├── outputs/               # 抠图结果输出目录
└── README.md             # 项目说明文档
```

## 📦 安装指南

### 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd segment
```

2. **创建虚拟环境**
```bash
python -m venv .venv
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **下载SAM2模型**
```bash
python download_models.py
```

模型文件将下载到 `checkpoints/` 目录：
- `sam2_tiny.pt` (149MB) - 最快，适合快速测试
- `sam2_small.pt` (176MB) - 平衡速度和精度
- `sam2_base_plus.pt` (309MB) - 高精度
- `sam2_large.pt` (856MB) - 最高精度

## 🚀 使用方法

### 启动应用
```bash
python app.py
```

应用将在 `http://localhost:7860` 启动

### 使用步骤

1. **📤 上传图片**：选择要处理的图片
2. **🔧 调整参数**：
   - 选择边缘处理方法
   - 调整平滑强度
   - 设置羽化半径
3. **🎯 选择模式**：
   - **单选模式**：点击区域直接导出
   - **多选模式**：选择多个区域后合并导出
4. **🖱️ 点击区域**：在彩色分割区域上点击选择
5. **📥 导出结果**：保存透明背景PNG图片

### 边缘平滑参数指南

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| 基础形态学 | 强度2-3 | 适合简单图像 |
| 软边缘 | 强度3-4，羽化3-5 | 适合人物肖像 |
| 综合处理 | 强度3-5，羽化3-7 | 适合复杂图像 |

## 🧪 测试功能

运行边缘平滑功能测试：
```bash
python test_edge_smoothing.py
```

测试结果将保存在 `test_outputs/` 目录中。

## 🔧 技术细节

### SAM2模型
- 使用Facebook Research的SAM2模型
- 支持4种不同尺寸的模型
- CPU模式运行，兼容性好

### 边缘平滑算法
- **形态学操作**：闭运算填充空洞，开运算平滑边缘
- **高斯模糊**：软化边缘细节
- **双边滤波**：保边去噪
- **距离变换**：实现边缘羽化效果
- **超采样抗锯齿**：上采样→模糊→下采样

### 图像处理流程
1. 原始图像resize到800px进行处理
2. SAM2生成分割masks
3. 用户点击选择区域
4. 将mask调整回原始尺寸
5. 应用边缘平滑算法
6. 生成RGBA透明背景图像

## 🎮 界面功能

### 主界面区域
- **左侧控制面板**：
  - 图片上传
  - 模式切换（单选/多选）
  - 模型选择（tiny/small/base_plus/large）
  - **边缘平滑参数调节**
- **右侧显示区域**：
  - 分割结果预览
  - 抠图结果展示
  - 文件下载

### 交互功能
- 🖱️ **点击选择**：直接点击分割区域
- 🔄 **模式切换**：单选和多选模式
- 🎨 **参数调节**：实时调整边缘平滑效果
- 📥 **一键导出**：自动生成时间戳文件名

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 检查网络连接
   - 重新运行 `python download_models.py`
   - 确认 `checkpoints/` 目录存在

2. **点击无响应**
   - 确保点击在彩色分割区域上
   - 检查图片是否正确上传
   - 尝试重新上传图片

3. **边缘效果不佳**
   - 尝试使用更高精度的模型（large）
   - 调整边缘平滑参数
   - 使用"综合处理"模式

4. **内存不足**
   - 使用更小的模型（tiny）
   - 压缩图片尺寸
   - 关闭其他应用程序

## 📈 性能优化

### 模型选择建议
- **快速测试**：使用 `tiny` 模型
- **平衡性能**：使用 `small` 或 `base_plus` 模型
- **最佳质量**：使用 `large` 模型

### 硬件建议
- **CPU**：4核以上推荐
- **内存**：8GB以上推荐
- **存储**：至少2GB空间用于模型文件

## 🤝 贡献指南

欢迎提交问题和改进建议！

1. Fork本项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

- [SAM2](https://github.com/facebookresearch/sam2) - Meta的Segment Anything Model 2
- [Gradio](https://gradio.app/) - 优秀的机器学习界面框架
- [OpenCV](https://opencv.org/) - 强大的计算机视觉库

---

**🎉 享受AI智能抠图的乐趣！** 