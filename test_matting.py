"""
测试 Matting 功能
"""

import numpy as np
import cv2
from PIL import Image
import os

# 导入我们的模块
from matting_model import MODNetWrapper, create_rgba_with_matte

def test_basic_matting():
    """测试基础 matting 功能"""
    print("=== 测试基础 Matting 功能 ===")
    
    # 创建测试图像
    # 创建一个有渐变背景的图像
    height, width = 400, 400
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 创建渐变背景
    for y in range(height):
        image[y, :] = [int(255 * y / height), 100, 255 - int(255 * y / height)]
    
    # 在中心添加一个圆形物体
    center = (width // 2, height // 2)
    radius = 100
    cv2.circle(image, center, radius, (255, 255, 255), -1)
    
    # 添加一些细节（模拟头发丝）
    for i in range(20):
        angle = i * np.pi / 10
        end_x = int(center[0] + (radius + 30) * np.cos(angle))
        end_y = int(center[1] + (radius + 30) * np.sin(angle))
        cv2.line(image, center, (end_x, end_y), (200, 200, 200), 2)
    
    # 创建对应的 mask
    mask = np.zeros((height, width), dtype=np.uint8)
    cv2.circle(mask, center, radius, 255, -1)
    
    # 为细节部分添加 mask
    for i in range(20):
        angle = i * np.pi / 10
        end_x = int(center[0] + (radius + 30) * np.cos(angle))
        end_y = int(center[1] + (radius + 30) * np.sin(angle))
        cv2.line(mask, center, (end_x, end_y), 255, 2)
    
    # 保存测试输入
    cv2.imwrite('test_outputs/test_input_image.png', cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    cv2.imwrite('test_outputs/test_input_mask.png', mask)
    
    # 初始化 matting 模型
    print("初始化 Matting 模型...")
    matting = MODNetWrapper()
    
    # 测试不同的 trimap 大小
    trimap_sizes = [5, 10, 20]
    
    for trimap_size in trimap_sizes:
        print(f"\n测试 trimap_size={trimap_size}")
        
        # 创建 trimap
        trimap = matting.create_trimap_from_mask(mask, trimap_size)
        cv2.imwrite(f'test_outputs/test_trimap_{trimap_size}.png', trimap)
        
        # 进行 matting
        matte = matting.predict(image, mask, trimap_size=trimap_size, quality="balanced")
        
        # 保存 matte
        matte_vis = (matte * 255).astype(np.uint8)
        cv2.imwrite(f'test_outputs/test_matte_{trimap_size}.png', matte_vis)
        
        # 创建 RGBA 图像
        result = create_rgba_with_matte(image, matte)
        result.save(f'test_outputs/test_result_{trimap_size}.png')
        
        print(f"  Matte 范围: [{matte.min():.3f}, {matte.max():.3f}]")
        print(f"  结果已保存")


def test_real_image_matting():
    """测试真实图像的 matting"""
    print("\n=== 测试真实图像 Matting ===")
    
    # 检查是否有测试图像
    test_images = ['test_outputs/cutout_single_*.png']
    
    # 创建一个简单的人像测试
    height, width = 600, 400
    image = np.ones((height, width, 3), dtype=np.uint8) * 200  # 灰色背景
    
    # 创建一个人形轮廓
    # 头部
    head_center = (width // 2, 150)
    cv2.ellipse(image, head_center, (80, 100), 0, 0, 360, (50, 30, 20), -1)
    
    # 身体
    body_pts = np.array([
        [width // 2 - 100, 250],
        [width // 2 + 100, 250],
        [width // 2 + 80, 400],
        [width // 2 - 80, 400]
    ], np.int32)
    cv2.fillPoly(image, [body_pts], (100, 80, 70))
    
    # 添加头发细节
    for i in range(30):
        angle = i * np.pi / 15 - np.pi / 2
        start_x = int(head_center[0] + 70 * np.cos(angle))
        start_y = int(head_center[1] - 90 + 70 * np.sin(angle))
        end_x = int(head_center[0] + 100 * np.cos(angle))
        end_y = int(head_center[1] - 90 + 100 * np.sin(angle))
        thickness = np.random.randint(1, 3)
        cv2.line(image, (start_x, start_y), (end_x, end_y), (20, 10, 5), thickness)
    
    # 创建对应的粗糙 mask
    mask = np.zeros((height, width), dtype=np.uint8)
    cv2.ellipse(mask, head_center, (80, 100), 0, 0, 360, 255, -1)
    cv2.fillPoly(mask, [body_pts], 255)
    
    # 保存
    cv2.imwrite('test_outputs/test_portrait_image.png', cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    cv2.imwrite('test_outputs/test_portrait_mask.png', mask)
    
    # 初始化 matting
    matting = MODNetWrapper()
    
    # 测试不同质量设置
    qualities = ["fast", "balanced", "high"]
    
    for quality in qualities:
        print(f"\n测试质量设置: {quality}")
        
        # 进行 matting
        matte = matting.predict(image, mask, trimap_size=15, quality=quality)
        
        # 细节增强
        enhanced_matte = matting.enhance_details(matte, enhancement=1.5)
        
        # 保存结果
        matte_vis = (matte * 255).astype(np.uint8)
        enhanced_vis = (enhanced_matte * 255).astype(np.uint8)
        
        cv2.imwrite(f'test_outputs/test_portrait_matte_{quality}.png', matte_vis)
        cv2.imwrite(f'test_outputs/test_portrait_matte_{quality}_enhanced.png', enhanced_vis)
        
        # 创建最终结果
        result = create_rgba_with_matte(image, enhanced_matte)
        result.save(f'test_outputs/test_portrait_result_{quality}.png')
        
        print(f"  处理完成")


def compare_with_original():
    """比较原始方法和 matting 方法"""
    print("\n=== 比较原始方法和 Matting 方法 ===")
    
    # 创建测试图像
    height, width = 300, 300
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 创建一个有锯齿边缘的 mask
    mask = np.zeros((height, width), dtype=np.uint8)
    points = []
    for i in range(12):
        angle = i * 2 * np.pi / 12
        if i % 2 == 0:
            r = 100
        else:
            r = 60
        x = int(width // 2 + r * np.cos(angle))
        y = int(height // 2 + r * np.sin(angle))
        points.append([x, y])
    
    points = np.array(points, np.int32)
    cv2.fillPoly(mask, [points], 255)
    
    # 保存原始 mask
    cv2.imwrite('test_outputs/compare_mask_original.png', mask)
    
    # 1. 原始方法（直接使用 mask）
    rgba_original = np.zeros((height, width, 4), dtype=np.uint8)
    rgba_original[:, :, :3] = image
    rgba_original[:, :, 3] = mask
    Image.fromarray(rgba_original, 'RGBA').save('test_outputs/compare_result_original.png')
    
    # 2. Matting 方法
    matting = MODNetWrapper()
    matte = matting.predict(image, mask, trimap_size=10, quality="high")
    result_matting = create_rgba_with_matte(image, matte)
    result_matting.save('test_outputs/compare_result_matting.png')
    
    # 3. 可视化 matte
    matte_vis = (matte * 255).astype(np.uint8)
    cv2.imwrite('test_outputs/compare_matte.png', matte_vis)
    
    print("比较结果已保存")


if __name__ == "__main__":
    # 创建输出目录
    os.makedirs('test_outputs', exist_ok=True)
    
    # 运行测试
    test_basic_matting()
    test_real_image_matting()
    compare_with_original()
    
    print("\n所有测试完成！请查看 test_outputs 目录中的结果。") 