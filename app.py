import gradio as gr
import numpy as np
from PIL import Image
import traceback
import tempfile
import os
import cv2
import time

# Import our custom modules
from sam_model import SAMPredictor
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    create_cutout_image, create_multi_cutout_image, find_mask_at_point, 
    filter_masks_by_area, resize_image_for_display, merge_masks
)

class AIImageCutout:
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize the AI Image Cutout application
        
        Args:
            model_size: SAM model size ('tiny', 'small', 'base_plus', 'large')
        """
        self.model_size = model_size
        self.predictor = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域
        self.segmentation_image = None
        self.last_cutout_file = None  # 保存最后生成的抠图文件路径
        self.selection_mode = "single"  # "single" or "multiple"
        
        # 边缘平滑参数默认值
        self.edge_method = "comprehensive"
        self.smooth_strength = 3
        self.feather_radius = 3
        
        # Initialize model
        self._init_model()
    
    def _init_model(self):
        """Initialize the SAM model"""
        try:
            print(f"Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size)
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            self.predictor = None
    
    def upload_image(self, image: Image.Image) -> tuple:
        """
        Handle image upload and initial processing
        
        Args:
            image: PIL Image from Gradio
            
        Returns:
            Tuple of (segmentation_image, status_message)
        """
        if image is None:
            return None, "请先上传图片"
        
        if self.predictor is None:
            return None, "模型未加载成功，请检查依赖安装"
        
        try:
            # Reset selections
            self.selected_masks = []
            
            # Convert PIL to numpy and save original
            self.current_image = pil_to_numpy(image)
            
            # Resize for display if too large
            self.display_image = resize_image_for_display(self.current_image, max_size=800)
            
            # Set image for SAM predictor using display image
            self.predictor.set_image(self.display_image)
            
            # Generate all masks
            print("Generating masks...")
            masks = self.predictor.predict_everything()
            
            # Filter masks by area to remove noise
            filtered_masks = filter_masks_by_area(masks, min_area=500)
            self.current_masks = filtered_masks
            
            if not filtered_masks:
                return numpy_to_pil(self.display_image), "未检测到足够大的区域，请尝试其他图片"
            
            # Visualize masks on display image
            self.segmentation_image = visualize_masks_with_borders(self.display_image, filtered_masks, self.selected_masks)
            
            status_msg = f"成功检测到 {len(filtered_masks)} 个可选区域。模式：{self.selection_mode}选择"
            return numpy_to_pil(self.segmentation_image), status_msg
            
        except Exception as e:
            error_msg = f"处理图片时出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg
    
    def toggle_selection_mode(self):
        """切换选择模式"""
        if self.selection_mode == "single":
            self.selection_mode = "multiple"
            self.selected_masks = []
            mode_text = "多选模式：点击多个区域，然后导出合并结果"
        else:
            self.selection_mode = "single"
            self.selected_masks = []
            mode_text = "单选模式：点击单个区域直接导出"
        
        # Update visualization
        if self.display_image is not None and self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(self.display_image, self.current_masks, self.selected_masks)
            return numpy_to_pil(self.segmentation_image), mode_text
        
        return None, mode_text
    
    def clear_selections(self):
        """清空所有选择"""
        self.selected_masks = []
        
        # Update visualization
        if self.display_image is not None and self.current_masks:
            self.segmentation_image = visualize_masks_with_borders(self.display_image, self.current_masks, self.selected_masks)
            return numpy_to_pil(self.segmentation_image), f"已清空选择。当前选择：{len(self.selected_masks)} 个区域"
        
        return None, "已清空选择"
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """
        Handle image click events
        
        Args:
            evt: Gradio SelectData event with click coordinates
            
        Returns:
            Tuple of (segmentation_image, cutout_image, status_message, download_file)
        """
        if self.current_image is None or self.display_image is None:
            return None, None, "请先上传并处理图片", None
        
        if not self.current_masks:
            return None, None, "没有检测到可选区域", None
        
        try:
            # Extract click coordinates from Gradio SelectData
            if evt is None or evt.index is None:
                return None, None, "无效的点击事件", None
            
            # Get coordinates from Gradio SelectData
            x, y = evt.index
            x, y = int(x), int(y)
            
            print(f"Click at: ({x}, {y})")
            
            # Find mask at clicked point
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return None, None, f"点击位置 ({x}, {y}) 没有检测到区域，请点击彩色区域", None
            
            if self.selection_mode == "single":
                # Single selection mode - direct export
                return self._process_single_selection(clicked_mask)
            else:
                # Multiple selection mode - toggle selection
                return self._process_multiple_selection(clicked_mask)
                
        except Exception as e:
            error_msg = f"处理点击事件失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _process_single_selection(self, clicked_mask: dict) -> tuple:
        """处理单选模式"""
        try:
            # 检查图像是否存在
            if self.current_image is None or self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # Get the mask
            mask = clicked_mask['segmentation']
            
            # 计算缩放比例
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            scale_x = original_w / display_w
            scale_y = original_h / display_h
            
            # 如果原图和显示图尺寸不同，需要调整mask尺寸
            if scale_x != 1.0 or scale_y != 1.0:
                # 将mask调整到原始图像大小
                mask_resized = cv2.resize(mask.astype(np.uint8), (original_w, original_h), interpolation=cv2.INTER_NEAREST)
                mask_resized = mask_resized.astype(bool)
            else:
                mask_resized = mask
            
            # 使用原始图像创建抠图（应用边缘平滑）
            cutout_image = create_cutout_image(
                self.current_image, 
                mask_resized,
                refine_edges=True,
                edge_method=self.edge_method,
                smooth_strength=self.smooth_strength,
                feather_radius=self.feather_radius
            )
            
            # 创建输出文件名（基于时间戳）
            timestamp = int(time.time())
            output_filename = f"cutout_single_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # 确保输出目录存在
            os.makedirs("outputs", exist_ok=True)
            
            # 保存抠图结果
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            status_msg = f"单选模式：成功抠出区域！面积: {clicked_mask['area']} 像素 | 边缘平滑: {self.edge_method} | 强度: {self.smooth_strength}"
            
            # 检查segmentation_image是否存在
            if self.segmentation_image is not None:
                return numpy_to_pil(self.segmentation_image), cutout_image, status_msg, output_path
            else:
                return None, cutout_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"单选抠图失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _process_multiple_selection(self, clicked_mask: dict) -> tuple:
        """处理多选模式"""
        try:
            # 检查图像是否存在
            if self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # Check if mask is already selected
            mask_id = id(clicked_mask)
            already_selected = any(id(mask) == mask_id for mask in self.selected_masks)
            
            if already_selected:
                # Remove from selection
                self.selected_masks = [mask for mask in self.selected_masks if id(mask) != mask_id]
                action = "取消选择"
            else:
                # Add to selection
                self.selected_masks.append(clicked_mask)
                action = "选择"
            
            # Update visualization
            self.segmentation_image = visualize_masks_with_borders(self.display_image, self.current_masks, self.selected_masks)
            
            status_msg = f"多选模式：{action}区域。当前选择：{len(self.selected_masks)} 个区域"
            return numpy_to_pil(self.segmentation_image), None, status_msg, None
            
        except Exception as e:
            error_msg = f"多选处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def export_selected(self) -> tuple:
        """导出选中的多个区域"""
        if not self.selected_masks:
            return None, "没有选中任何区域，请先选择区域", None
        
        if self.current_image is None:
            return None, "请先上传图片", None
        
        try:
            # Get all selected masks
            selected_mask_arrays = []
            
            # 计算缩放比例
            original_h, original_w = self.current_image.shape[:2]
            
            # 检查display_image是否存在
            if self.display_image is None:
                return None, "显示图像未加载", None
            
            display_h, display_w = self.display_image.shape[:2]
            
            scale_x = original_w / display_w
            scale_y = original_h / display_h
            
            for mask_dict in self.selected_masks:
                mask = mask_dict['segmentation']
                
                # 如果原图和显示图尺寸不同，需要调整mask尺寸
                if scale_x != 1.0 or scale_y != 1.0:
                    mask_resized = cv2.resize(mask.astype(np.uint8), (original_w, original_h), interpolation=cv2.INTER_NEAREST)
                    mask_resized = mask_resized.astype(bool)
                else:
                    mask_resized = mask
                
                selected_mask_arrays.append(mask_resized)
            
            # 创建多区域合并抠图（应用边缘平滑）
            cutout_image = create_multi_cutout_image(
                self.current_image, 
                selected_mask_arrays,
                refine_edges=True,
                edge_method=self.edge_method,
                smooth_strength=self.smooth_strength,
                feather_radius=self.feather_radius
            )
            
            # 创建输出文件名（基于时间戳）
            timestamp = int(time.time())
            output_filename = f"cutout_multi_{len(self.selected_masks)}regions_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # 确保输出目录存在
            os.makedirs("outputs", exist_ok=True)
            
            # 保存抠图结果
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            # 计算总面积
            total_area = sum(mask_dict['area'] for mask_dict in self.selected_masks)
            
            status_msg = f"成功导出 {len(self.selected_masks)} 个区域的合并抠图！总面积: {total_area} 像素 | 边缘平滑: {self.edge_method} | 强度: {self.smooth_strength}"
            return cutout_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg, None

    def update_edge_settings(self, edge_method: str, smooth_strength: int, feather_radius: int):
        """更新边缘平滑设置并重新生成当前抠图"""
        self.edge_method = edge_method
        self.smooth_strength = smooth_strength
        self.feather_radius = feather_radius
        
        method_name = {
            "basic": "基础形态学",
            "soft": "软边缘",
            "comprehensive": "综合处理"
        }.get(edge_method, edge_method)
        
        # 如果当前有选中的区域，重新生成抠图
        if self.selection_mode == "single" and self.last_cutout_file:
            # 重新生成单选抠图
            if self.current_image is not None and len(self.current_masks) > 0:
                clicked_mask = self.current_masks[-1]  # 使用最后一个选择的mask
                _, cutout_image, status_msg, output_path = self._process_single_selection(clicked_mask)
                return cutout_image, f"边缘平滑设置已更新: {method_name} | 强度: {smooth_strength} | 羽化: {feather_radius}", output_path
        
        elif self.selection_mode == "multiple" and self.selected_masks:
            # 重新生成多选抠图
            cutout_image, status_msg, output_path = self.export_selected()
            return cutout_image, status_msg, output_path
        
        return None, f"边缘平滑设置已更新: {method_name} | 强度: {smooth_strength} | 羽化: {feather_radius}", None

    def create_interface(self):
        """Create Gradio interface"""
        with gr.Blocks(title="AI智能抠图工具 - 多选版") as demo:
            gr.Markdown("""
            # 🎨 AI智能抠图工具 - 多选版
            
            ## 📋 功能说明：
            - **单选模式**：点击单个区域直接导出抠图
            - **多选模式**：点击多个区域，合并导出为一张图
            
            ## 🔧 使用方法：
            1. 📤 上传图片
            2. 🔀 选择单选/多选模式
            3. 🖱️ 点击彩色区域（多选模式下可选择多个）
            4. 📥 导出透明背景PNG图片
            
            ---
            """)
            
            with gr.Row():
                with gr.Column(scale=1):
                    # Input section
                    gr.Markdown("### 📤 上传图片")
                    input_image = gr.Image(
                        type="pil",
                        label="选择图片",
                        sources=["upload"],
                        interactive=True
                    )
                    
                    upload_btn = gr.Button("🔄 分析图片", variant="primary", size="lg")
                    
                    # Selection mode controls
                    gr.Markdown("### 🔀 选择模式")
                    mode_toggle_btn = gr.Button("切换到多选模式", variant="secondary", size="lg")
                    
                    # Multiple selection controls
                    gr.Markdown("### 🎯 多选控制")
                    with gr.Row():
                        export_btn = gr.Button("📥 导出选中区域", variant="primary")
                        clear_btn = gr.Button("🗑️ 清空选择", variant="secondary")
                    
                    # Model selection
                    gr.Markdown("### ⚙️ 模型设置")
                    model_selector = gr.Dropdown(
                        choices=["tiny", "small", "base_plus", "large"],
                        value="tiny",
                        label="模型大小",
                        info="tiny: 最快，large: 最准确",
                        interactive=True
                    )
                    
                    # Edge smoothing controls
                    gr.Markdown("### 🎨 边缘平滑")
                    edge_method_selector = gr.Dropdown(
                        choices=["basic", "soft", "comprehensive"],
                        value="comprehensive",
                        label="边缘处理方法",
                        info="基础形态学、软边缘、综合处理",
                        interactive=True
                    )
                    smooth_strength_slider = gr.Slider(
                        minimum=1,
                        maximum=10,
                        value=3,
                        label="平滑强度",
                        info="控制边缘的平滑程度",
                        interactive=True
                    )
                    feather_radius_slider = gr.Slider(
                        minimum=1,
                        maximum=10,
                        value=3,
                        label="羽化半径",
                        info="控制边缘的羽化程度",
                        interactive=True
                    )
                    
                    # Instructions
                    gr.Markdown("### 📝 使用提示")
                    instructions = gr.Markdown("""
                    **当前：单选模式**
                    - 点击彩色区域直接导出
                    - 右键保存结果图片
                    """)
                    
                with gr.Column(scale=2):
                    # Output section
                    gr.Markdown("### 🎯 区域选择")
                    segmentation_output = gr.Image(
                        type="pil",
                        label="可选区域（点击进行选择）",
                        interactive=True
                    )
                    
                    gr.Markdown("### 🖼️ 抠图结果")
                    cutout_output = gr.Image(
                        type="pil",
                        label="抠图结果（透明背景PNG）",
                        interactive=False
                    )
                    
                    # Download button
                    download_btn = gr.File(
                        label="💾 下载透明背景PNG图片",
                        interactive=False
                    )
            
            # Status display
            status_display = gr.Textbox(
                label="状态信息",
                interactive=False,
                max_lines=3,
                value="请上传图片开始使用"
            )
            
            # Event handlers
            upload_btn.click(
                fn=self.upload_image,
                inputs=[input_image],
                outputs=[segmentation_output, status_display]
            )
            
            # Mode toggle
            def toggle_mode_handler():
                segmentation_img, status_msg = self.toggle_selection_mode()
                if self.selection_mode == "single":
                    btn_text = "切换到多选模式"
                    instructions_text = """
                    **当前：单选模式**
                    - 点击彩色区域直接导出
                    - 右键保存结果图片
                    """
                else:
                    btn_text = "切换到单选模式"
                    instructions_text = """
                    **当前：多选模式**
                    - 点击多个彩色区域选择
                    - 选中区域显示红色边框
                    - 点击"导出选中区域"合并导出
                    """
                return segmentation_img, status_msg, btn_text, instructions_text
            
            mode_toggle_btn.click(
                fn=toggle_mode_handler,
                outputs=[segmentation_output, status_display, mode_toggle_btn, instructions]
            )
            
            # Clear selections
            clear_btn.click(
                fn=self.clear_selections,
                outputs=[segmentation_output, status_display]
            )
            
            # Export selected
            export_btn.click(
                fn=self.export_selected,
                outputs=[cutout_output, status_display, download_btn]
            )
            
            # Image click handler
            segmentation_output.select(
                fn=self.on_image_click,
                outputs=[segmentation_output, cutout_output, status_display, download_btn]
            )
            
            # Auto-process on image upload
            input_image.change(
                fn=self.upload_image,
                inputs=[input_image],
                outputs=[segmentation_output, status_display]
            )
            
            # Model change handler
            def change_model(new_model_size):
                self.model_size = new_model_size
                self._init_model()
                return f"模型已切换到: {new_model_size}"
            
            model_selector.change(
                fn=change_model,
                inputs=[model_selector],
                outputs=[status_display]
            )

            # Edge smoothing settings handler
            def update_edge_settings_handler(edge_method, smooth_strength, feather_radius):
                cutout_image, status_msg, download_file = self.update_edge_settings(edge_method, smooth_strength, feather_radius)
                return cutout_image, status_msg, download_file
            
            edge_method_selector.change(
                fn=update_edge_settings_handler,
                inputs=[edge_method_selector, smooth_strength_slider, feather_radius_slider],
                outputs=[cutout_output, status_display, download_btn]
            )
            smooth_strength_slider.change(
                fn=update_edge_settings_handler,
                inputs=[edge_method_selector, smooth_strength_slider, feather_radius_slider],
                outputs=[cutout_output, status_display, download_btn]
            )
            feather_radius_slider.change(
                fn=update_edge_settings_handler,
                inputs=[edge_method_selector, smooth_strength_slider, feather_radius_slider],
                outputs=[cutout_output, status_display, download_btn]
            )
        
        return demo

def main():
    """Main function to run the application"""
    print("🚀 启动AI智能抠图工具 - 多选版...")
    
    # Create application instance
    app = AIImageCutout(model_size="tiny")  # Start with tiny model for faster loading
    
    # Create and launch interface
    demo = app.create_interface()
    
    # Launch with configuration
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_api=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main() 