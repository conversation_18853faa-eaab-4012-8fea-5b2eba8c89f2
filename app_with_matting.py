import gradio as gr
import numpy as np
from PIL import Image
import traceback
import tempfile
import os
import cv2
import time

# Import our custom modules
from sam_model import SAMPredictor
from matting_model import MODNetWrapper, create_rgba_with_matte
from utils import (
    pil_to_numpy, numpy_to_pil, visualize_masks, visualize_masks_with_borders,
    find_mask_at_point, filter_masks_by_area, resize_image_for_display, merge_masks
)

class AIImageCutoutWithMatting:
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize the AI Image Cutout application with Matting support
        
        Args:
            model_size: SAM model size ('tiny', 'small', 'base_plus', 'large')
        """
        self.model_size = model_size
        self.predictor = None
        self.matting_model = None
        self.current_image = None  # 原始图像
        self.display_image = None  # 调整后的显示图像
        self.current_masks = []
        self.selected_masks = []  # 存储选中的区域
        self.segmentation_image = None
        self.last_cutout_file = None  # 保存最后生成的抠图文件路径
        self.selection_mode = "single"  # "single" or "multiple"
        
        # Matting 参数
        self.use_matting = True  # 是否使用 matting
        self.trimap_size = 10  # trimap 不确定区域大小
        self.matting_quality = "balanced"  # matting 质量
        self.detail_enhancement = 1.0  # 细节增强程度
        
        # Initialize models
        self._init_models()
    
    def _init_models(self):
        """Initialize the SAM and Matting models"""
        try:
            # 初始化 SAM 模型
            print(f"Loading SAM2 {self.model_size} model...")
            self.predictor = SAMPredictor(model_size=self.model_size)
            print("SAM model loaded successfully!")
            
            # 初始化 Matting 模型
            print("Loading Matting model...")
            self.matting_model = MODNetWrapper()
            print("Matting model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading models: {e}")
            self.predictor = None
            self.matting_model = None
    
    def upload_image(self, image: Image.Image) -> tuple:
        """
        Handle image upload and initial processing
        
        Args:
            image: PIL Image from Gradio
            
        Returns:
            Tuple of (segmentation_image, status_message)
        """
        if image is None:
            return None, "请先上传图片"
        
        if self.predictor is None:
            return None, "模型未加载成功，请检查依赖安装"
        
        try:
            # Reset selections
            self.selected_masks = []
            
            # Convert PIL to numpy and save original
            self.current_image = pil_to_numpy(image)
            
            # Resize for display if too large
            self.display_image = resize_image_for_display(self.current_image, max_size=800)
            
            # Set image for SAM predictor using display image
            self.predictor.set_image(self.display_image)
            
            # Generate all masks
            print("Generating masks...")
            masks = self.predictor.predict_everything()
            
            # Filter masks by area to remove noise
            filtered_masks = filter_masks_by_area(masks, min_area=500)
            self.current_masks = filtered_masks
            
            if not filtered_masks:
                return numpy_to_pil(self.display_image), "未检测到足够大的区域，请尝试其他图片"
            
            # Visualize masks on display image
            self.segmentation_image = visualize_masks_with_borders(self.display_image, filtered_masks, self.selected_masks)
            
            status_msg = f"成功检测到 {len(filtered_masks)} 个可选区域。模式：{self.selection_mode}选择 | Matting: {'开启' if self.use_matting else '关闭'}"
            return numpy_to_pil(self.segmentation_image), status_msg
            
        except Exception as e:
            print(f"Error uploading image: {e}")
            traceback.print_exc()
            return None, f"上传图片失败: {str(e)}"
    
    def on_image_click(self, evt: gr.SelectData) -> tuple:
        """
        Handle click on segmentation image
        
        Args:
            evt: Gradio SelectData event containing click coordinates
            
        Returns:
            Tuple of (segmentation_image, cutout_image, status_message, download_file)
        """
        if not self.current_masks:
            return None, None, "请先上传图片", None
        
        try:
            # Get click coordinates
            x, y = evt.index
            print(f"Click at: ({x}, {y})")
            
            # Find the mask at the clicked point
            clicked_mask = find_mask_at_point(self.current_masks, (x, y))
            
            if clicked_mask is None:
                return numpy_to_pil(self.segmentation_image), None, "未找到该位置的区域", None
            
            # Process based on selection mode
            if self.selection_mode == "single":
                return self._process_single_selection(clicked_mask)
            else:
                return self._process_multiple_selection(clicked_mask)
                
        except Exception as e:
            print(f"Error processing click: {e}")
            traceback.print_exc()
            return None, None, f"处理失败: {str(e)}", None
    
    def _process_single_selection(self, clicked_mask: dict) -> tuple:
        """处理单选模式"""
        try:
            # 检查图像是否存在
            if self.current_image is None or self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # Get the mask
            mask = clicked_mask['segmentation']
            
            # 计算缩放比例
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            # 将mask调整到原始图像大小
            if original_h != display_h or original_w != display_w:
                mask = cv2.resize(
                    mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # 使用 Matting 或传统方法创建抠图
            if self.use_matting and self.matting_model is not None:
                # 使用 Matting 方法
                matte = self.matting_model.predict(
                    self.current_image,
                    mask,
                    trimap_size=self.trimap_size,
                    quality=self.matting_quality
                )
                
                # 细节增强
                if self.detail_enhancement != 1.0:
                    matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                
                # 创建 RGBA 图像
                cutout_image = create_rgba_with_matte(self.current_image, matte)
                
            else:
                # 使用传统方法（向后兼容）
                from utils import create_cutout_image
                cutout_image = create_cutout_image(
                    self.current_image, 
                    mask,
                    refine_edges=True,
                    edge_method="comprehensive",
                    smooth_strength=3,
                    feather_radius=3
                )
            
            # 创建输出文件名（基于时间戳）
            timestamp = int(time.time())
            output_filename = f"cutout_single_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # 确保输出目录存在
            os.makedirs("outputs", exist_ok=True)
            
            # 保存抠图结果
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            status_msg = f"单选模式：成功抠出区域！面积: {clicked_mask['area']} 像素"
            if self.use_matting:
                status_msg += f" | Matting: trimap={self.trimap_size}, 质量={self.matting_quality}"
            
            # 检查segmentation_image是否存在
            if self.segmentation_image is not None:
                return numpy_to_pil(self.segmentation_image), cutout_image, status_msg, output_path
            else:
                return None, cutout_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"单选抠图失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def _process_multiple_selection(self, clicked_mask: dict) -> tuple:
        """处理多选模式"""
        try:
            # 检查图像是否存在
            if self.display_image is None:
                return None, None, "图像未加载，请先上传图片", None
            
            # Check if mask is already selected
            mask_id = id(clicked_mask)
            already_selected = any(id(mask) == mask_id for mask in self.selected_masks)
            
            if already_selected:
                # Remove from selection
                self.selected_masks = [mask for mask in self.selected_masks if id(mask) != mask_id]
                action = "取消选择"
            else:
                # Add to selection
                self.selected_masks.append(clicked_mask)
                action = "选择"
            
            # Update visualization
            self.segmentation_image = visualize_masks_with_borders(self.display_image, self.current_masks, self.selected_masks)
            
            status_msg = f"多选模式：{action}区域。当前选择：{len(self.selected_masks)} 个区域"
            return numpy_to_pil(self.segmentation_image), None, status_msg, None
            
        except Exception as e:
            error_msg = f"多选处理失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, None, error_msg, None
    
    def export_selected(self) -> tuple:
        """导出选中的多个区域"""
        if not self.selected_masks:
            return None, "没有选中任何区域，请先选择区域", None
        
        if self.current_image is None:
            return None, "请先上传图片", None
        
        try:
            # 获取所有选中的 masks
            masks = []
            
            # 计算缩放比例
            original_h, original_w = self.current_image.shape[:2]
            display_h, display_w = self.display_image.shape[:2]
            
            for mask_dict in self.selected_masks:
                mask = mask_dict['segmentation']
                
                # 将mask调整到原始图像大小
                if original_h != display_h or original_w != display_w:
                    mask = cv2.resize(
                        mask.astype(np.uint8), 
                        (original_w, original_h), 
                        interpolation=cv2.INTER_NEAREST
                    ).astype(bool)
                
                masks.append(mask)
            
            # 合并所有 masks
            combined_mask = merge_masks(masks)
            
            # 使用 Matting 或传统方法创建抠图
            if self.use_matting and self.matting_model is not None:
                # 使用 Matting 方法
                matte = self.matting_model.predict(
                    self.current_image,
                    combined_mask,
                    trimap_size=self.trimap_size,
                    quality=self.matting_quality
                )
                
                # 细节增强
                if self.detail_enhancement != 1.0:
                    matte = self.matting_model.enhance_details(matte, self.detail_enhancement)
                
                # 创建 RGBA 图像
                cutout_image = create_rgba_with_matte(self.current_image, matte)
                
            else:
                # 使用传统方法
                from utils import create_multi_cutout_image
                cutout_image = create_multi_cutout_image(
                    self.current_image,
                    masks,
                    refine_edges=True,
                    edge_method="comprehensive",
                    smooth_strength=3,
                    feather_radius=3
                )
            
            # 创建输出文件名
            timestamp = int(time.time())
            output_filename = f"cutout_multi_{timestamp}.png"
            output_path = os.path.join("outputs", output_filename)
            
            # 确保输出目录存在
            os.makedirs("outputs", exist_ok=True)
            
            # 保存抠图结果
            cutout_image.save(output_path, 'PNG')
            self.last_cutout_file = output_path
            
            status_msg = f"成功导出 {len(self.selected_masks)} 个区域的合并抠图！"
            if self.use_matting:
                status_msg += f" | 使用 Matting 增强"
            
            return cutout_image, status_msg, output_path
            
        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, error_msg, None
    
    def toggle_selection_mode(self, mode: str) -> str:
        """切换选择模式"""
        self.selection_mode = mode
        self.selected_masks = []  # 切换模式时清空选择
        
        # 如果有图像，更新可视化
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
        
        return f"已切换到{mode}模式"
    
    def clear_selection(self) -> tuple:
        """清空当前选择"""
        self.selected_masks = []
        
        # 更新可视化
        if self.segmentation_image is not None and self.display_image is not None:
            self.segmentation_image = visualize_masks_with_borders(
                self.display_image, self.current_masks, self.selected_masks
            )
            return numpy_to_pil(self.segmentation_image), "已清空所有选择"
        
        return None, "已清空所有选择"
    
    def update_matting_params(self, use_matting: bool, trimap_size: int, 
                            quality: str, enhancement: float) -> str:
        """更新 Matting 参数"""
        self.use_matting = use_matting
        self.trimap_size = trimap_size
        self.matting_quality = quality
        self.detail_enhancement = enhancement
        
        status = f"Matting 参数已更新: "
        if use_matting:
            status += f"开启, trimap={trimap_size}, 质量={quality}, 增强={enhancement}"
        else:
            status += "关闭（使用传统方法）"
        
        return status


def create_interface():
    """Create the Gradio interface"""
    
    # Initialize the application
    app = AIImageCutoutWithMatting(model_size="tiny")
    
    with gr.Blocks(title="AI智能抠图工具 - 增强版") as demo:
        gr.Markdown("""
        # 🎨 AI智能抠图工具 - 增强版
        
        使用 SAM + Matting 技术实现高质量抠图，特别适合处理头发丝、透明物体等细节。
        
        ### 使用说明：
        1. 上传图片，系统会自动检测可选区域
        2. 点击想要抠出的区域
        3. 使用 Matting 技术自动优化边缘细节
        4. 下载透明背景的PNG图片
        
        ### 新功能：
        - 🔥 **Matting 技术**：自动处理头发丝、半透明边缘
        - 🎯 **多区域选择**：可以选择多个区域合并抠图
        - ⚡ **质量控制**：快速/平衡/高质量三种模式
        - 🔍 **细节增强**：可调节边缘细节增强程度
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # 图片上传
                input_image = gr.Image(
                    label="上传图片",
                    type="pil",
                    elem_id="input_image"
                )
                
                # 选择模式
                with gr.Row():
                    selection_mode = gr.Radio(
                        choices=["single", "multiple"],
                        value="single",
                        label="选择模式",
                        info="单选：点击直接抠图 | 多选：选择多个区域后导出"
                    )
                
                # Matting 设置
                with gr.Accordion("🎨 Matting 设置", open=True):
                    use_matting = gr.Checkbox(
                        value=True,
                        label="启用 Matting 增强",
                        info="使用高级算法优化边缘细节"
                    )
                    
                    with gr.Row():
                        trimap_size = gr.Slider(
                            minimum=5,
                            maximum=30,
                            step=5,
                            value=10,
                            label="边缘过渡区域",
                            info="控制边缘处理的范围"
                        )
                        
                        matting_quality = gr.Radio(
                            choices=["fast", "balanced", "high"],
                            value="balanced",
                            label="处理质量",
                            info="质量越高，处理越慢"
                        )
                    
                    detail_enhancement = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        step=0.1,
                        value=1.0,
                        label="细节增强",
                        info="增强头发丝等细节的清晰度"
                    )
                    
                    update_params_btn = gr.Button("更新参数", size="sm")
                
                # 多选模式按钮
                with gr.Row(visible=True) as multi_select_buttons:
                    clear_btn = gr.Button("清空选择", size="sm")
                    export_btn = gr.Button("导出选中区域", variant="primary", size="sm")
                
                # 状态信息
                status_text = gr.Textbox(
                    label="状态",
                    interactive=False,
                    value="请上传图片开始"
                )
            
            with gr.Column(scale=1):
                # 分割结果展示
                segmentation_output = gr.Image(
                    label="点击选择区域",
                    type="pil",
                    interactive=True,
                    elem_id="segmentation_output"
                )
                
                # 抠图结果
                cutout_output = gr.Image(
                    label="抠图结果",
                    type="pil",
                    elem_id="cutout_output"
                )
                
                # 下载按钮
                download_btn = gr.File(
                    label="下载PNG文件",
                    visible=True,
                    elem_id="download_btn"
                )
        
        # 事件处理
        
        # 上传图片
        input_image.upload(
            fn=app.upload_image,
            inputs=[input_image],
            outputs=[segmentation_output, status_text]
        )
        
        # 点击分割图像
        segmentation_output.select(
            fn=app.on_image_click,
            inputs=[],
            outputs=[segmentation_output, cutout_output, status_text, download_btn]
        )
        
        # 切换选择模式
        selection_mode.change(
            fn=app.toggle_selection_mode,
            inputs=[selection_mode],
            outputs=[status_text]
        )
        
        # 清空选择
        clear_btn.click(
            fn=app.clear_selection,
            inputs=[],
            outputs=[segmentation_output, status_text]
        )
        
        # 导出选中区域
        export_btn.click(
            fn=app.export_selected,
            inputs=[],
            outputs=[cutout_output, status_text, download_btn]
        )
        
        # 更新 Matting 参数
        update_params_btn.click(
            fn=app.update_matting_params,
            inputs=[use_matting, trimap_size, matting_quality, detail_enhancement],
            outputs=[status_text]
        )
        
        # 根据选择模式显示/隐藏按钮
        def update_ui_visibility(mode):
            return gr.update(visible=(mode == "multiple"))
        
        selection_mode.change(
            fn=update_ui_visibility,
            inputs=[selection_mode],
            outputs=[multi_select_buttons]
        )
    
    return demo


def main():
    """Main function to run the application"""
    print("🚀 启动AI智能抠图工具 - 增强版...")
    
    # Create and launch the interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )


if __name__ == "__main__":
    main() 