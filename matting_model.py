"""
MODNet Matting Model Integration
用于高质量抠图的 Matting 模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from PIL import Image
import cv2
import os
import requests
from typing import Tuple, Optional

class MODNetWrapper:
    """MODNet 模型封装类"""
    
    def __init__(self, ckpt_path: Optional[str] = None):
        """
        初始化 MODNet 模型
        
        Args:
            ckpt_path: 模型权重文件路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.ckpt_path = ckpt_path or os.path.join('checkpoints', 'modnet_photographic_portrait_matting.ckpt')
        
        # 下载并加载模型
        self._download_model_if_needed()
        self._load_model()
    
    def _download_model_if_needed(self):
        """暂时跳过模型下载，使用引导滤波方法"""
        pass
    
    def _load_model(self):
        """加载 MODNet 模型"""
        # 暂时使用引导滤波方法，不需要加载完整模型
        print("使用引导滤波 matting 方法（无需下载大型模型）")
    
    def create_trimap_from_mask(self, mask: np.ndarray, dilate_size: int = 10) -> np.ndarray:
        """
        从二值 mask 创建 trimap
        
        Args:
            mask: 二值 mask (0 或 255)
            dilate_size: 膨胀大小，用于创建不确定区域
            
        Returns:
            trimap: 0=背景, 128=不确定, 255=前景
        """
        # 确保 mask 是二值的
        if mask.max() <= 1:
            mask = mask * 255
        
        mask = mask.astype(np.uint8)
        
        # 创建 trimap
        trimap = np.zeros_like(mask)
        trimap[mask > 127] = 255  # 前景
        
        # 创建不确定区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (dilate_size, dilate_size))
        dilated = cv2.dilate(mask, kernel, iterations=1)
        eroded = cv2.erode(mask, kernel, iterations=1)
        
        # 不确定区域 = 膨胀区域 - 腐蚀区域
        uncertain = dilated - eroded
        trimap[uncertain > 0] = 128
        
        return trimap
    
    def preprocess_image(self, image: np.ndarray, ref_size: int = 512) -> torch.Tensor:
        """
        预处理图像用于 MODNet
        
        Args:
            image: 输入图像 (H, W, 3)
            ref_size: 参考尺寸
            
        Returns:
            预处理后的张量
        """
        # 获取原始尺寸
        h, w = image.shape[:2]
        
        # 计算新尺寸（保持纵横比）
        if h > w:
            new_h = ref_size
            new_w = int(ref_size * w / h)
        else:
            new_w = ref_size
            new_h = int(ref_size * h / w)
        
        # 确保尺寸是 32 的倍数（MODNet 要求）
        new_h = (new_h + 31) // 32 * 32
        new_w = (new_w + 31) // 32 * 32
        
        # 调整大小
        image_resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        # 转换为张量
        image_tensor = torch.from_numpy(image_resized).float() / 255.0
        image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0)  # (1, 3, H, W)
        
        # 标准化
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
        image_tensor = (image_tensor - mean) / std
        
        return image_tensor.to(self.device), (h, w)
    
    def predict(self, image: np.ndarray, mask: np.ndarray, 
                trimap_size: int = 10, quality: str = "balanced") -> np.ndarray:
        """
        使用 MODNet 进行抠图
        
        Args:
            image: 输入图像 (H, W, 3)
            mask: SAM 生成的二值 mask
            trimap_size: trimap 不确定区域大小
            quality: 质量设置 ("fast", "balanced", "high")
            
        Returns:
            alpha matte (H, W) 范围 [0, 1]
        """
        with torch.no_grad():
            # 创建 trimap
            trimap = self.create_trimap_from_mask(mask, trimap_size)
            
            # 预处理图像
            ref_size = {"fast": 384, "balanced": 512, "high": 768}.get(quality, 512)
            image_tensor, original_size = self.preprocess_image(image, ref_size)
            
            # 如果有实际的 MODNet 模型，这里应该调用模型
            # matte = self.model(image_tensor, True)[0]
            
            # 临时实现：使用引导滤波改进 mask
            # 这是一个简化的实现，实际应该使用真正的 MODNet
            matte = self._guided_filter_matting(image, mask, trimap)
            
            return matte
    
    def _guided_filter_matting(self, image: np.ndarray, mask: np.ndarray, 
                              trimap: np.ndarray) -> np.ndarray:
        """
        使用改进的引导滤波进行 matting
        """
        # 将 mask 转换为浮点数
        if mask.max() > 1:
            mask = mask.astype(np.float32) / 255.0
        else:
            mask = mask.astype(np.float32)
        
        # 获取不确定区域
        uncertain_mask = (trimap == 128)
        
        # 多尺度引导滤波
        guide = image.astype(np.float32) / 255.0
        
        # 使用多个尺度的引导滤波
        scales = [10, 20, 40]
        eps_values = [1e-4, 1e-3, 1e-2]
        
        refined_alphas = []
        for radius, eps in zip(scales, eps_values):
            # 简单的引导滤波实现
            refined = self._simple_guided_filter(guide, mask, radius, eps)
            refined_alphas.append(refined)
        
        # 融合多尺度结果
        final_alpha = np.mean(refined_alphas, axis=0)
        
        # 边缘增强
        # 计算梯度
        grad_x = cv2.Sobel(final_alpha, cv2.CV_32F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(final_alpha, cv2.CV_32F, 0, 1, ksize=3)
        grad_mag = np.sqrt(grad_x**2 + grad_y**2)
        
        # 在边缘区域增强对比度
        edge_mask = grad_mag > 0.1
        final_alpha[edge_mask] = self._sigmoid_enhance(final_alpha[edge_mask])
        
        # 只在不确定区域应用细化结果
        result = mask.copy()
        result[uncertain_mask] = final_alpha[uncertain_mask]
        
        # 确保值在 [0, 1] 范围内
        result = np.clip(result, 0, 1)
        
        return result
    
    def _simple_guided_filter(self, guide: np.ndarray, src: np.ndarray, 
                             radius: int, eps: float) -> np.ndarray:
        """简单的引导滤波实现"""
        # 如果是彩色图像，转换为灰度
        if len(guide.shape) == 3:
            guide = cv2.cvtColor(guide, cv2.COLOR_RGB2GRAY)
        
        # 均值滤波
        mean_guide = cv2.boxFilter(guide, cv2.CV_32F, (radius, radius))
        mean_src = cv2.boxFilter(src, cv2.CV_32F, (radius, radius))
        
        # 协方差和方差
        corr_guide_src = cv2.boxFilter(guide * src, cv2.CV_32F, (radius, radius))
        corr_guide = cv2.boxFilter(guide * guide, cv2.CV_32F, (radius, radius))
        
        # 计算系数
        var_guide = corr_guide - mean_guide * mean_guide
        cov_guide_src = corr_guide_src - mean_guide * mean_src
        
        a = cov_guide_src / (var_guide + eps)
        b = mean_src - a * mean_guide
        
        # 应用滤波
        mean_a = cv2.boxFilter(a, cv2.CV_32F, (radius, radius))
        mean_b = cv2.boxFilter(b, cv2.CV_32F, (radius, radius))
        
        return mean_a * guide + mean_b
    
    def _sigmoid_enhance(self, x: np.ndarray, k: float = 10) -> np.ndarray:
        """使用 sigmoid 函数增强对比度"""
        return 1 / (1 + np.exp(-k * (x - 0.5)))
    
    def enhance_details(self, matte: np.ndarray, enhancement: float = 1.0) -> np.ndarray:
        """
        增强 matte 的细节
        
        Args:
            matte: alpha matte
            enhancement: 增强程度
            
        Returns:
            增强后的 matte
        """
        if enhancement == 1.0:
            return matte
        
        # 使用锐化滤波器增强边缘
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]]) * (enhancement - 1) * 0.1
        kernel[1, 1] = 1 + kernel[1, 1]
        
        enhanced = cv2.filter2D(matte, -1, kernel)
        return np.clip(enhanced, 0, 1)


def create_rgba_with_matte(image: np.ndarray, matte: np.ndarray) -> Image.Image:
    """
    使用 alpha matte 创建 RGBA 图像
    
    Args:
        image: 原始图像 (H, W, 3)
        matte: alpha matte (H, W) 范围 [0, 1]
        
    Returns:
        RGBA PIL 图像
    """
    # 确保尺寸匹配
    if matte.shape[:2] != image.shape[:2]:
        matte = cv2.resize(matte, (image.shape[1], image.shape[0]))
    
    # 创建 RGBA 图像
    rgba = np.zeros((image.shape[0], image.shape[1], 4), dtype=np.uint8)
    rgba[:, :, :3] = image
    rgba[:, :, 3] = (matte * 255).astype(np.uint8)
    
    return Image.fromarray(rgba, 'RGBA')


# 简单的测试函数
def test_matting():
    """测试 matting 功能"""
    print("测试 Matting 功能...")
    
    # 创建测试图像和 mask
    test_image = np.ones((256, 256, 3), dtype=np.uint8) * 255
    test_mask = np.zeros((256, 256), dtype=np.uint8)
    cv2.circle(test_mask, (128, 128), 80, 255, -1)
    
    # 初始化 matting 模型
    matting = MODNetWrapper()
    
    # 进行 matting
    matte = matting.predict(test_image, test_mask)
    
    print(f"Matte shape: {matte.shape}, range: [{matte.min():.2f}, {matte.max():.2f}]")
    
    # 创建 RGBA 图像
    result = create_rgba_with_matte(test_image, matte)
    
    print("Matting 测试完成！")
    return result


if __name__ == "__main__":
    test_matting() 