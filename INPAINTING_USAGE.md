# 🎨 AI图像补全功能使用说明

## 🚀 **双模式工作原理**

### 🖼️ **抠图模式 (Cutout Mode)**
**目的**: 提取图像中的特定对象，生成透明背景的PNG图片

**交互流程**:
1. 上传图片 → 系统自动生成可选区域（彩色区域）
2. 点击要抠出的区域 → 立即生成抠图结果
3. 调整Matting参数 → 实时更新抠图效果
4. 下载透明背景PNG文件

**特色功能**:
- **SAM模型选择**: tiny(最快) → small → base_plus → large(最准确)
- **Matting增强**: 自动处理头发丝、半透明边缘
- **实时参数调整**: 滑动参数立即看到效果变化

### 🔧 **补全模式 (Inpainting Mode)**
**目的**: 修复/删除图像中的不需要内容，智能填补空白区域

**交互流程**:
1. 上传图片 → 系统自动生成可选区域
2. **选择多个区域**: 点击要删除/修复的区域（可选择多个）
3. 选中区域会显示红色高亮预览
4. 点击"执行补全"按钮 → 生成修复后的图片
5. 下载完整修复的图片

**补全算法**:
- **Telea**: 适合小面积修复
- **Navier-Stokes**: 适合线性结构
- **Fast Marching**: 速度最快

## 🎯 **详细操作步骤**

### **步骤1: 上传图片**
```
支持格式: JPG, PNG, BMP, TIFF
建议尺寸: 不超过2048x2048像素
```

### **步骤2: 选择模式**
- **抠图模式**: 单击区域 → 立即抠图
- **补全模式**: 多选区域 → 点击"执行补全"

### **步骤3A: 抠图模式操作**
1. 点击任意彩色区域
2. 系统立即生成抠图结果
3. 实时调整参数：
   - **边缘过渡区域**: 5-30 (控制边缘处理范围)
   - **处理质量**: fast/balanced/high
   - **细节增强**: 0.5-2.0 (增强头发丝等细节)

### **步骤3B: 补全模式操作**
1. 点击要删除的区域（可选择多个）
2. 选中区域显示红色高亮
3. 调整补全参数：
   - **补全算法**: 根据内容类型选择
   - **补全半径**: 1-20 (影响补全范围)
4. 点击"执行补全"按钮

## 🔧 **参数详解**

### **SAM模型参数**
- **tiny**: 最快速度，适合实时预览
- **small**: 平衡速度和精度
- **base_plus**: 更高精度，处理复杂场景
- **large**: 最高精度，处理困难案例

### **Matting参数** (仅抠图模式)
- **启用Matting增强**: 使用高级算法优化边缘
- **边缘过渡区域**: 控制边缘的模糊过渡范围
- **处理质量**: 
  - fast: 快速处理
  - balanced: 平衡质量和速度
  - high: 最高质量
- **细节增强**: 增强头发丝、毛发等细节的清晰度

### **补全参数** (仅补全模式)
- **补全算法**:
  - **telea**: 适合小面积修复，纹理平滑
  - **ns**: 适合线性结构，保持几何特征
  - **fast_marching**: 速度最快，适合大面积
- **补全半径**: 影响补全时考虑的周围像素范围

## 🌟 **最佳实践**

### **抠图模式技巧**
1. **选择合适的SAM模型**: 复杂场景用large，简单场景用tiny
2. **启用Matting**: 处理有头发、毛发的对象
3. **调整细节增强**: 增强边缘细节，但不要过度
4. **实时预览**: 滑动参数立即看到效果

### **补全模式技巧**
1. **精确选择**: 只选择需要删除的部分
2. **选择合适算法**: 
   - 删除文字/水印 → telea
   - 删除线性物体 → ns
   - 大面积修复 → fast_marching
3. **多次补全**: 复杂场景可分多次处理
4. **预览确认**: 利用红色高亮确认选择区域

## ⚠️ **注意事项**

1. **图片尺寸**: 过大图片会自动缩放到800px进行处理
2. **模型切换**: 切换SAM模型会重新分析图片
3. **模式切换**: 切换模式会清空当前选择
4. **内存使用**: large模型需要更多内存
5. **处理时间**: 高质量设置需要更长处理时间

## 🎯 **输出文件**

### **抠图模式**
- 格式: PNG (透明背景)
- 命名: `cutout_时间戳.png`
- 保存位置: `outputs/` 目录

### **补全模式**
- 格式: PNG/JPG (保持原格式)
- 命名: `inpaint_result_时间戳.png`
- 保存位置: `outputs/` 目录

## 🔗 **访问地址**

启动后访问: `http://localhost:7870`

## 🆘 **常见问题**

**Q: 抠图边缘不自然怎么办？**
A: 启用Matting增强，调整边缘过渡区域和细节增强参数

**Q: 补全效果不理想？**
A: 尝试不同的补全算法，或分多次小面积处理

**Q: 处理速度太慢？**
A: 使用tiny模型和fast质量设置

**Q: 无法选择到精确区域？**
A: 尝试使用large模型获得更精确的区域检测 